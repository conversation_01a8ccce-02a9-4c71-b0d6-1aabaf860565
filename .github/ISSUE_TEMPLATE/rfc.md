---
name: RFC (Request for Comments)
about: Propose a change or idea for discussion
labels:
- RFC
---

# RFC: [Title]

## What is an RFC?
An RFC (Request for Comments) is a proposal for significant changes or additions to the project,
such as new features, major refactorings, or process improvements. RFCs are meant for ideas that
could have a broad impact and benefit from wider community discussion.

## Before you proceed

- Please search existing issues and discussions to see if your idea has already been proposed or is under consideration.
- To maintain a focused and manageable discussion space, we kindly ask that you limit opening multiple RFCs within a short period (days or weeks). This helps ensure each proposal receives the attention it deserves and prevents overwhelming the community and maintainers.

## Context

What would you like to propose? This could be improvements, refinements, optimizations, process changes, or other significant topics.

### Considerations

What should we keep in mind? Include relevant context, such as tradeoffs (e.g., performance vs. complexity), technical feasibility, user impact, or other concerns.
