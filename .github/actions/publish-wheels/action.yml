name: Publish wheels (R2)
description: Publish wheels to Cloudflare R2
inputs:
  cleanup-local-dist:
    description: 'Cleanup local dist directory after publishing to R2'
    required: false
    default: 'true'

runs:
  using: "composite"
  steps:
    - name: Checkout repository
      # https://github.com/actions/checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - name: Download built wheels
      # https://github.com/actions/download-artifact
      uses: actions/download-artifact@95815c38cf2ff2164869cbab79da8d1f422bc89e # v4.2.1
      with:
        path: dist/
        pattern: "*.whl"

    - name: Configure AWS CLI for Cloudflare R2
      shell: bash
      run: |
        set -euo pipefail
        echo "Configuring AWS CLI for Cloudflare R2..."

        # Install specific AWS CLI version known to work with R2
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64-2.22.35.zip" -o "awscliv2.zip"
        unzip awscliv2.zip
        sudo ./aws/install --bin-dir /usr/local/bin --install-dir /usr/local/aws-cli --update

        mkdir -p ~/.aws
        envsubst < .github/aws/credentials.ini > ~/.aws/credentials
        envsubst < .github/aws/config.ini > ~/.aws/config

    - name: Upload new wheels to Cloudflare R2
      shell: bash
      run: |
        bash ./scripts/ci/publish-wheels-r2-upload-new-wheels.sh

    - name: Remove old wheels from Cloudflare R2
      shell: bash
      run: |
        bash ./scripts/ci/publish-wheels-r2-remove-old-wheels.sh

    - name: Generate index.html
      shell: bash
      run: |
        bash ./scripts/ci/publish-wheels-generate-index.sh

    - name: Upload index.html to Cloudflare R2
      shell: bash
      run: |
        bash ./scripts/ci/publish-wheels-r2-upload-index.sh

    - name: Verify uploaded files in Cloudflare R2
      shell: bash
      run: |
        bash ./scripts/ci/publish-wheels-r2-verify-files.sh

    - name: Clean up local dist directory
      if: ${{ inputs.cleanup-local-dist == 'true' && success() }}
      shell: bash
      run: |
        set -euo pipefail

        if [[ -d dist ]]; then
          echo "Found dist directory:"
          ls -lh dist/
          rm -rf dist/*
          echo "Cleanup completed"
        else
          echo "No dist directory found"
        fi
