name: Upload wheel artifact to GitHub Actions
description: Upload wheel artifact to GitHub Actions

runs:
  using: "composite"
  steps:
    - name: Set release output
      shell: bash
      if: github.event_name == 'push'
      id: vars
      run: |
        if [ ! -d "./dist" ]; then
          echo "Error: dist directory not found"
          exit 1
        fi

        ASSET_PATH=$(find ./dist -name "*.whl" -type f | xargs ls -t 2>/dev/null | head -n 1)

        if [ -z "$ASSET_PATH" ]; then
          echo "Error: No .whl files found in dist directory"
          exit 1
        fi

        echo "ASSET_PATH=$ASSET_PATH" >> $GITHUB_ENV
        echo "ASSET_NAME=$(basename "$ASSET_PATH")" >> $GITHUB_ENV

    - name: Upload wheel artifact
      if: github.event_name == 'push'
      # https://github.com/actions/upload-artifact
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      with:
        name: ${{ env.ASSET_NAME }}
        path: ${{ env.ASSET_PATH }}
