name: build-docs

on:
  push:
    branches: [master, nightly]

jobs:
  build-docs:
    runs-on: ubuntu-latest
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@4d991eb9b905ef189e4c376166672c3f2f230481 # v2.11.0
        with:
          egress-policy: audit

      - name: Fire event to nautilus_docs
        run: |
          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.REPOSITORY_ACCESS_TOKEN }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/nautechsystems/nautilus_docs/dispatches \
            -d '{"event_type":"push"}'
