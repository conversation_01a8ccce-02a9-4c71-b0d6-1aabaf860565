name: performance

on:
  push:
    branches: [nightly]

jobs:
  performance-benchmarks:
    runs-on: ubuntu-latest
    env:
      BUILD_MODE: release
      RUST_BACKTRACE: 1
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DB: nautilus
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@4d991eb9b905ef189e4c376166672c3f2f230481 # v2.11.0
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: "3.12"
          free-disk-space: "true"

      - name: Run pre-commit # Runs again here to check pre-commit with Python 3.12
        run: make pre-commit

      - name: Install Nautilus CLI and run init postgres
        run: |
          make install-cli
          nautilus database init --schema ${{ github.workspace }}/schema/sql
        env:
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USERNAME: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DATABASE: nautilus

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      - name: Run nautilus_core tests
        run: make cargo-test

      - name: Build and install wheel
        uses: ./.github/actions/common-wheel-build
        with:
          python-version: "3.12"
          github_ref: ${{ github.ref }}

      # Run codspeed once only
      - name: Run benchmarks
        # https://github.com/CodSpeedHQ/action
        uses: CodSpeedHQ/action@0010eb0ca6e89b80c88e8edaaa07cfe5f3e6664d # v3.5.0
        with:
          token: ${{ secrets.CODSPEED_TOKEN }}
          run: uv run --no-sync pytest tests/performance_tests --benchmark-disable-gc --codspeed
