[package]
name = "nautilus-cli"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-model = { workspace = true }
nautilus-core = { workspace = true }
nautilus-infrastructure = { workspace = true , features = ["postgres"] }

anyhow = { workspace = true }
log = { workspace = true }
tokio = { workspace = true }
clap = { version = "4.5.38", features = ["derive", "env"] }
dotenvy = { version = "0.15.7" }
simple_logger = "5.0.0"

[[bin]]
name = "nautilus"
path = "src/bin/cli.rs"
