[package]
name = "nautilus-common"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_common"
crate-type = ["rlib", "staticlib"]

[features]
default = ["indicators", "rstest"]
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "nautilus-indicators/extension-module",
]
ffi = [
  "cbindgen",
  "nautilus-core/ffi",
  "nautilus-model/ffi",
]
"clock_v2" = []
indicators = ["nautilus-indicators"]
python = [
  "pyo3",
  "pyo3-async-runtimes",
  "nautilus-core/python",
  "nautilus-model/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-indicators = { workspace = true, optional = true }

ahash = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
async-stream = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
derive_builder = { workspace = true }
futures = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
rstest = { workspace = true , optional = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
strum = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
ustr = { workspace = true }
uuid = { workspace = true }
regex = "1.11.1"
sysinfo = "0.35.1"

[dev-dependencies]
proptest = { workspace = true }
tempfile = { workspace = true }
criterion = {workspace = true}
rand = { workspace = true }
regex = "1.11.1"

[build-dependencies]
cbindgen = { workspace = true, optional = true }

[[bench]]
name = "cache_orders"
path = "benches/cache/orders.rs"
harness = false

[[bench]]
name = "matching"
path = "benches/matching.rs"
harness = false
