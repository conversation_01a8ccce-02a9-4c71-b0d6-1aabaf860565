[package]
name = "nautilus-data"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_data"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "nautilus-persistence/extension-module",
]
ffi = [
  "nautilus-common/ffi",
  "nautilus-core/ffi",
  "nautilus-model/ffi",
  "nautilus-persistence/ffi",
]
python = [
  "pyo3",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-model/python",
  "nautilus-persistence/python",
]
clock_v2 = ["nautilus-common/clock_v2"]
high-precision = ["nautilus-model/high-precision"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-persistence = { workspace = true }

ahash = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
ustr = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
rstest = { workspace = true }
