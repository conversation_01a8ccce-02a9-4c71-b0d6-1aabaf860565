[package]
name = "nautilus-live"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_live"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-data/extension-module",
  "nautilus-execution/extension-module",
  "nautilus-model/extension-module",
  "nautilus-portfolio/extension-module",
  "nautilus-risk/extension-module",
]
ffi = [
  "nautilus-common/ffi",
  "nautilus-core/ffi",
  "nautilus-model/ffi",
]
python = [
  "pyo3",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-data/python",
  "nautilus-execution/python",
  "nautilus-model/python",
  "nautilus-portfolio/python",
  "nautilus-risk/python",
]
clock_v2 = ["nautilus-common/clock_v2"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-data = { workspace = true }
nautilus-execution = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-persistence = { workspace = true }
nautilus-portfolio = { workspace = true }
nautilus-risk = { workspace = true }
nautilus-system = { workspace = true }

anyhow = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
futures = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
serde = { workspace = true }
strum = { workspace = true }
tokio = { workspace = true }
ustr = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
rstest = { workspace = true }
