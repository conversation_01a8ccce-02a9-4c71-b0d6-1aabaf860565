[package]
name = "nautilus-model"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_model"
crate-type = ["rlib", "staticlib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
]
ffi = ["cbindgen", "nautilus-core/ffi"]
python = ["pyo3", "nautilus-core/python"]
stubs = ["rstest"]
high-precision = []
defi = []

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }

anyhow = { workspace = true }
chrono = { workspace = true }
derive_builder = { workspace = true }
enum_dispatch = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
rstest = { workspace = true, optional = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
strum = { workspace = true }
thiserror = { workspace = true }
thousands = { workspace = true }
ustr = { workspace = true }
evalexpr = "=11.3.1"  # Pinned to v11.3.1 for MIT licensing
implied-vol = { version = "1.0.1", features = ["normal-distribution"] }
tabled = "0.19.0"

[dev-dependencies]
criterion = { workspace = true }
float-cmp = { workspace = true }
iai = { workspace = true }

[build-dependencies]
cbindgen = { workspace = true, optional = true }

[[bench]]
name = "book_iai"
path = "benches/book_iai.rs"
harness = false

[[bench]]
name = "fixed_precision_criterion"
path = "benches/fixed_precision_criterion.rs"
harness = false

[[bench]]
name = "fixed_precision_iai"
path = "benches/fixed_precision_iai.rs"
harness = false
