[package]
name = "nautilus-risk"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_risk"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-execution/extension-module",
  "nautilus-model/extension-module",
]
python = [
  "pyo3",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-execution/python",
  "nautilus-model/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-execution = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-portfolio = { workspace = true }

log = { workspace = true }
pyo3 = { workspace = true, optional = true }
rust_decimal = { workspace = true }
thiserror = { workspace = true }
ustr = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
rstest = { workspace = true }
