[package]
name = "nautilus-system"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_system"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
    "pyo3/extension-module",
]
python = [
    "pyo3",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-data = { workspace = true }
nautilus-execution = { workspace = true }
nautilus-infrastructure = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-persistence = { workspace = true }
nautilus-portfolio = { workspace = true }
nautilus-risk = { workspace = true }
nautilus-trading = { workspace = true }
hostname = "0.4.1"

anyhow = { workspace = true }
futures = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
tokio = { workspace = true }
ustr = { workspace = true }

[dev-dependencies]
rstest = { workspace = true }
